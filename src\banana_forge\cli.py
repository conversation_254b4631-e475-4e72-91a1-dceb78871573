"""
Banana Forge CLI - AI-powered feature implementation plan generator.

This module provides the command-line interface for Banana Forge, allowing users
to generate structured feature implementation plans using AI agents.
"""

import typer
from typing import Optional
from pathlib import Path

from .core import run_generation
from .config import settings

app = typer.Typer(
    name="banana-forge",
    help="AI-powered CLI tool for generating structured feature implementation plans",
    add_completion=False,
)


@app.command()
def generate(
    feature: str = typer.Argument(
        ...,
        help="Feature description or name to generate implementation plan for"
    ),
    input_file: Optional[Path] = typer.Option(
        None,
        "--input",
        "-i",
        help="Optional input file with additional context or specifications",
        exists=True,
        file_okay=True,
        dir_okay=False,
    ),
    output_file: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Optional output file to save the generated plan (default: stdout)",
        file_okay=True,
        dir_okay=False,
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose logging to see detailed progress",
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run",
        help="Show what would be done without actually calling AI models",
    ),
) -> None:
    """
    Generate a structured feature implementation plan using AI agents.
    
    This command takes a feature description and produces a comprehensive
    Markdown report following the 10-agent template structure.
    
    Examples:
        banana-forge generate "OAuth login system"
        banana-forge generate "Real-time chat feature" --verbose
        banana-forge generate "Payment integration" -o payment_plan.md
    """
    try:
        # Set verbose mode in settings if requested
        if verbose:
            settings.verbose = True
            typer.echo("Verbose mode enabled", err=True)
        
        if dry_run:
            typer.echo("DRY RUN MODE - No AI models will be called", err=True)
        
        # Read additional context from input file if provided
        additional_context = ""
        if input_file:
            try:
                additional_context = input_file.read_text(encoding="utf-8")
                if verbose:
                    typer.echo(f"Loaded additional context from {input_file}", err=True)
            except Exception as e:
                typer.echo(f"Error reading input file: {e}", err=True)
                raise typer.Exit(1)
        
        # Generate the implementation plan
        if verbose:
            typer.echo(f"Generating implementation plan for: {feature}", err=True)
        
        result = run_generation(
            feature_description=feature,
            additional_context=additional_context,
            dry_run=dry_run,
        )
        
        # Output the result
        if output_file:
            try:
                output_file.write_text(result, encoding="utf-8")
                typer.echo(f"Plan saved to {output_file}", err=True)
            except Exception as e:
                typer.echo(f"Error writing output file: {e}", err=True)
                raise typer.Exit(1)
        else:
            typer.echo(result)
        
        if verbose:
            typer.echo("Generation completed successfully", err=True)
            
    except KeyboardInterrupt:
        typer.echo("\nOperation cancelled by user", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {e}", err=True)
        if verbose:
            import traceback
            typer.echo(traceback.format_exc(), err=True)
        raise typer.Exit(1)


@app.command()
def version() -> None:
    """Show the version of Banana Forge."""
    from . import __version__
    typer.echo(f"Banana Forge v{__version__}")


@app.command()
def config() -> None:
    """Show current configuration settings."""
    typer.echo("Current Banana Forge Configuration:")
    typer.echo(f"  OpenRouter API Key: {'Set' if settings.openrouter_api_key else 'Not set'}")
    typer.echo(f"  Ollama Base URL: {settings.ollama_base_url}")
    typer.echo(f"  Primary Model: {settings.primary_model}")
    typer.echo(f"  Local Model: {settings.local_model}")
    typer.echo(f"  Max Concurrent Agents: {settings.max_concurrent_agents}")
    typer.echo(f"  ChromaDB Path: {settings.chroma_db_path}")
    typer.echo(f"  Log Level: {settings.log_level}")


def main() -> None:
    """Main entry point for the CLI application."""
    app()


if __name__ == "__main__":
    main()
